# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "0751e3713f9f29e2d8f3609e4fa902e1"
name = "Khodkar AI sales agent"
handle = "khodkar-sales-agent"
application_url = "https://shop-chat-agent.com"
embedded = true

[build]
include_config_on_deploy = true

[webhooks]
api_version = "2025-04"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "customer_read_customers,customer_read_orders,customer_read_store_credit_account_transactions,customer_read_store_credit_accounts,unauthenticated_read_product_listings"

[auth]
redirect_urls = [ "https://shop-chat-agent.com/api/auth" ]

[pos]
embedded = false

[mcp.customer_authentication]
redirect_uris = [
  "https://shop-chat-agent.com/callback"
]
