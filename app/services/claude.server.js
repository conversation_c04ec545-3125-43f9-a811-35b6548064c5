/**
 * Claude Service
 * Manages interactions with the Claude API
 */
import { anthropic } from "@ai-sdk/anthropic";
import { streamText } from "ai";
import AppConfig from "./config.server.js";
import systemPrompts from "../prompts/prompts.json" with { type: "json" };

/**
 * Creates a Claude service instance
 * @param {string} apiKey - Claude API key
 * @returns {Object} Claude service with methods for interacting with Claude API
 */
export function createClaudeService(apiKey = process.env.CLAUDE_API_KEY) {
  // Initialize Claude client with AI SDK
  const claudeModel = anthropic(AppConfig.api.defaultModel, {
    apiKey: apiKey
  });

  /**
   * Streams a conversation with <PERSON>
   * @param {Object} params - Stream parameters
   * @param {Array} params.messages - Conversation history
   * @param {string} params.promptType - The type of system prompt to use
   * @param {Array} params.tools - Available tools for Claude
   * @param {Object} streamHandlers - Stream event handlers
   * @param {Function} streamHandlers.onText - <PERSON>les text chunks
   * @param {Function} streamHandlers.onMessage - <PERSON><PERSON> complete messages
   * @param {Function} streamHandlers.onToolUse - Handles tool use requests
   * @returns {Promise<Object>} The final message
   */
  const streamConversation = async ({
    messages,
    promptType = AppConfig.api.defaultPromptType,
    tools
  }, streamHandlers) => {
    // Get system prompt from configuration or use default
    const systemInstruction = getSystemPrompt(promptType);

    try {
      // Create stream using AI SDK
      const result = streamText({
        model: claudeModel,
        system: systemInstruction,
        messages: messages,
        maxTokens: AppConfig.api.maxTokens,
        tools: tools && tools.length > 0 ? tools : undefined,
      });

      let fullText = '';

      // Process the stream
      for await (const delta of result.textStream) {
        fullText += delta;
        if (streamHandlers.onText) {
          streamHandlers.onText(delta);
        }
      }

      // Get the final results using the promise properties
      const [finishReason, toolCalls] = await Promise.all([
        result.finishReason,
        result.toolCalls
      ]);

      // Create a message object compatible with the existing interface
      const finalMessage = {
        role: 'assistant',
        content: [{ type: 'text', text: fullText }],
        stop_reason: finishReason === 'stop' ? 'end_turn' : finishReason
      };

      // Handle tool calls if present
      if (toolCalls && toolCalls.length > 0) {
        for (const toolCall of toolCalls) {
          const toolContent = {
            type: 'tool_use',
            id: toolCall.toolCallId,
            name: toolCall.toolName,
            input: toolCall.args
          };
          finalMessage.content.push(toolContent);

          if (streamHandlers.onToolUse) {
            await streamHandlers.onToolUse(toolContent);
          }
        }
      }

      // Call onMessage handler
      if (streamHandlers.onMessage) {
        streamHandlers.onMessage(finalMessage);
      }

      return finalMessage;

    } catch (error) {
      console.error('Error in streamConversation:', error);
      // Create error message
      const errorMessage = {
        role: 'assistant',
        content: [{ type: 'text', text: 'Sorry, I encountered an error processing your request.' }],
        stop_reason: 'error'
      };

      if (streamHandlers.onMessage) {
        streamHandlers.onMessage(errorMessage);
      }

      return errorMessage;
    }
  };

  /**
   * Gets the system prompt content for a given prompt type
   * @param {string} promptType - The prompt type to retrieve
   * @returns {string} The system prompt content
   */
  const getSystemPrompt = (promptType) => {
    return systemPrompts.systemPrompts[promptType]?.content ||
      systemPrompts.systemPrompts[AppConfig.api.defaultPromptType].content;
  };

  return {
    streamConversation,
    getSystemPrompt
  };
}

export default {
  createClaudeService
};
